#!/usr/bin/env node

/**
 * 自动修复代码格式错误的脚本
 * 主要修复：
 * 1. HTML标签闭合问题
 * 2. Vue模板语法错误
 * 3. TypeScript类型问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要修复的文件列表
const filesToFix = [
  'src/views/chatting/discord.vue',
  'src/views/chatting/line.vue',
  'src/views/configure/QCtask.vue',
  'src/views/homePage/overview.vue',
  'src/views/qualityControl/components/opsQCDcChat.vue',
  'src/views/qualityControl/discordQC.vue',
];

// HTML标签闭合修复规则
const htmlFixRules = [
  {
    // 修复 span 标签闭合问题
    pattern: /(<span[^>]*>[\s\S]*?)\s*<\/span>\s*<\/el-descriptions-item>/g,
    replacement: '$1</span>\n                    </el-descriptions-item>',
  },
  {
    // 修复 template 标签闭合问题
    pattern: /(<template[^>]*>[\s\S]*?)\s*<\/template>\s*<template/g,
    replacement: '$1</template>\n            <template',
  },
  {
    // 修复 el-descriptions-item 标签闭合问题
    pattern: /(\{\{[^}]+\}\})\s*<\/el-descriptions-item>/g,
    replacement: '$1\n                  </el-descriptions-item>',
  },
];

// Vue模板修复规则
const vueTemplateFixRules = [
  {
    // 为 v-for 添加 key 属性
    pattern: /<([^>]+)\s+v-for="([^"]+)"\s+(?!.*:key)([^>]*)>/g,
    replacement: '<$1 v-for="$2" :key="$2.split(\',\')[1] || $2.split(\',\')[0]" $3>',
  },
  {
    // 将 v-if 移到包装元素
    pattern: /<([^>]+)\s+v-for="([^"]+)"\s+([^>]*)\s+v-if="([^"]+)"([^>]*)>/g,
    replacement:
      '<template v-for="$2" :key="$2.split(\',\')[1] || $2.split(\',\')[0]">\n      <$1 $3 v-if="$4" $5>\n    </template>',
  },
];

// TypeScript类型修复规则
const typeScriptFixRules = [
  {
    // 替换 any 类型为更具体的类型
    pattern: /:\s*any\[\]/g,
    replacement: ': unknown[]',
  },
  {
    // 替换函数参数中的 any
    pattern: /\(([^:]+):\s*any\)/g,
    replacement: '($1: unknown)',
  },
  {
    // 替换变量声明中的 any
    pattern: /ref<any>/g,
    replacement: 'ref<unknown>',
  },
];

/**
 * 修复单个文件
 * @param {string} filePath 文件路径
 */
function fixFile(filePath) {
  try {
    console.log(`正在修复文件: ${filePath}`);

    if (!fs.existsSync(filePath)) {
      console.warn(`文件不存在: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 应用HTML修复规则
    htmlFixRules.forEach(rule => {
      const newContent = content.replace(rule.pattern, rule.replacement);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
        console.log(`  - 应用HTML修复规则`);
      }
    });

    // 应用Vue模板修复规则
    vueTemplateFixRules.forEach(rule => {
      const newContent = content.replace(rule.pattern, rule.replacement);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
        console.log(`  - 应用Vue模板修复规则`);
      }
    });

    // 应用TypeScript修复规则
    typeScriptFixRules.forEach(rule => {
      const newContent = content.replace(rule.pattern, rule.replacement);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
        console.log(`  - 应用TypeScript修复规则`);
      }
    });

    // 如果有变更，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✓ 文件已修复: ${filePath}`);
    } else {
      console.log(`  - 文件无需修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`修复文件失败 ${filePath}:`, error.message);
  }
}

/**
 * 修复特定的HTML语法错误
 */
function fixSpecificHtmlErrors() {
  console.log('\n开始修复特定的HTML语法错误...');

  // 修复 discord.vue 中的标签闭合问题
  const discordPath = 'src/views/chatting/discord.vue';
  if (fs.existsSync(discordPath)) {
    let content = fs.readFileSync(discordPath, 'utf8');

    // 修复第560行的标签闭合问题
    content = content.replace(
      /(\{\{[^}]+\}\})\s*<\/el-descriptions-item>/g,
      '$1\n                  </el-descriptions-item>'
    );

    fs.writeFileSync(discordPath, content, 'utf8');
    console.log('  ✓ 修复 discord.vue 标签闭合问题');
  }

  // 修复其他文件的类似问题...
  // 这里可以添加更多特定的修复逻辑
}

/**
 * 主函数
 */
function main() {
  console.log('开始自动修复代码格式错误...\n');

  // 修复特定的HTML语法错误
  fixSpecificHtmlErrors();

  // 修复文件列表中的所有文件
  filesToFix.forEach(fixFile);

  console.log('\n代码格式错误修复完成！');
  console.log('\n建议接下来运行以下命令：');
  console.log('1. npm run lint:fix  # 自动修复ESLint错误');
  console.log('2. npm run format    # 格式化代码');
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
