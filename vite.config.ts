/*
 * @Author: wenh<PERSON>.wang
 * @Date: 2024-04-09 17:08:33
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-12-03 11:42:05
 */
import legacy from '@vitejs/plugin-legacy';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import AutoImport from 'unplugin-auto-import/vite';
import ElementPlus from 'unplugin-element-plus/vite';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';
import { manualChunksPlugin } from 'vite-plugin-webpackchunkname';

import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

export default defineConfig({
  base: '/',
  server: {
    host: '0.0.0.0',
    port: 8088,
    open: false,
    hmr: true,
    proxy: {
      '/gateway': {
        target: 'https://admin-gateway-test.funplus.com/',
        changeOrigin: true,
        ws: false,
        secure: false,
        rewrite: path => path.replace(/^\/gateway/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
      },
    },
    minify: 'terser',
    // terserOptions: {
    //   compress: {
    //     drop_console: false,
    //     drop_debugger: true
    //   }
    // }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  plugins: [
    vue(),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      eslintrc: {
        enabled: true,
      },
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
    }),
    Components({
      deep: true,
      dirs: ['src/packages', 'src/views/chatting/mail/components'],
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
    }),
    ElementPlus({
      useSource: true,
    }),
    legacy({
      targets: ['defaults', 'ie >= 11', 'chrome 44', 'not dead', '> 1%'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      renderLegacyChunks: true,
      polyfills: [
        'es.symbol',
        'es.array.filter',
        'es.promise',
        'es.promise.finally',
        'es/map',
        'es/set',
        'es.array.for-each',
        'es.object.define-properties',
        'es.object.define-property',
        'es.object.get-own-property-descriptor',
        'es.object.get-own-property-descriptors',
        'es.object.keys',
        'es.object.to-string',
        'web.dom-collections.for-each',
        'esnext.global-this',
        'esnext.string.match-all',
      ],
    }),
    visualizer({
      template: 'treemap',
      gzipSize: true,
      brotliSize: true,
      filename: 'analyse.html',
    }),
    manualChunksPlugin(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        charset: false,
        additionalData: [`@use "@/assets/styles/element-variables.scss" as *;`],
      },
    },
  },
});
