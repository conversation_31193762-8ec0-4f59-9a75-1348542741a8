<template>
  <el-scrollbar style="height: 100%">
    <div class="operating-space" v-if="activeEmailInfo.thread_id">
      <!-- 顶部基础信息 -->
      <div class="base-info">
        <el-descriptions
          direction="horizontal"
          size="small"
          :title="$t('text_basic_info')"
          :column="3"
          border
        >
          <template #extra>
            <span class="basic-info-title">
              {{ $t('text_processor') }}：{{ activeEmailInfo.processor || $t('text_none') }}
            </span>
          </template>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('text_title') }}</span>
            </template>
            {{ activeEmailInfo.subject || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('text_email_status') }}</span>
            </template>
            <el-tag :type="getStatusType(activeEmailInfo.status)" size="small">
              {{ getStatusText(activeEmailInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('text_tags') }}</span>
            </template>
            <div v-if="activeEmailInfo.tags && activeEmailInfo.tags.length > 0">
              <el-tag
                v-for="tag in activeEmailInfo.tags"
                :key="tag"
                size="small"
                style="margin-right: 5px"
              >
                {{ tag }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('text_remark') }}</span>
            </template>
            {{ activeEmailInfo.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 邮件内容区域 -->
      <EmailContent
        :active-email-info="activeEmailInfo"
        @download-attachment="handleDownloadAttachment"
      />

      <!-- 邮件回复处理区域 -->
      <EmailReply
        :active-email-info="activeEmailInfo"
        :reply-form="replyForm"
        :reply-loading="replyLoading"
        :no-permission="noPermission"
        :is-uploading="isUploading"
        @reply="handleReply"
        @remark="handleRemark"
        @edit-tag="handleEditTag"
        @update:reply-form="updateReplyForm"
        @update:is-uploading="updateIsUploading"
      />
    </div>
    <el-empty v-else :description="$t('info_no_data')" />
  </el-scrollbar>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useUserInfoStore } from '@/stores';
import EmailContent from './EmailContent.vue';
import EmailReply from './EmailReply.vue';

interface EmailInfo {
  thread_id?: string;
  processor?: string;
  subject?: string;
  status?: string;
  tags?: string[];
  remark?: string;
  messages?: any[];
}

interface ReplyForm {
  reply_type: string;
  reply_body: string;
}

interface Props {
  activeEmailInfo: EmailInfo;
  replyForm: ReplyForm;
  replyLoading: boolean;
  isUploading: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'reply', opType: number): void;
  (event: 'remark'): void;
  (event: 'edit-tag'): void;
  (event: 'download-attachment', attachment: any): void;
  (event: 'update:reply-form', value: ReplyForm): void;
  (event: 'update:is-uploading', value: boolean): void;
}>();

const { t: $t } = useI18n();
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>);

// 判断是否不可操作
const noPermission = computed(() => {
  return props.activeEmailInfo.last_reply_service === userInfo.value.username;
});

// 获取状态类型
const getStatusType = (status?: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'closed':
      return 'info';
    case 'archived':
      return 'warning';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status?: string) => {
  switch (status) {
    case 'active':
      return '活跃';
    case 'closed':
      return '已关闭';
    case 'archived':
      return '已归档';
    default:
      return '未知';
  }
};

const handleReply = (opType: number) => {
  emit('reply', opType);
};

const handleRemark = () => {
  emit('remark');
};

const handleEditTag = () => {
  emit('edit-tag');
};

const handleDownloadAttachment = (attachment: any) => {
  emit('download-attachment', attachment);
};

const updateReplyForm = (value: ReplyForm) => {
  emit('update:reply-form', value);
};

const updateIsUploading = (value: boolean) => {
  emit('update:is-uploading', value);
};
</script>

<style lang="scss" scoped>
.operating-space {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 5px 15px;

  .base-info {
    width: 100%;
    margin-bottom: 10px;

    :deep(.el-descriptions__title) {
      width: 100% !important;
    }

    :deep(.el-descriptions__header) {
      margin-bottom: 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 10px 0;
    }

    :deep(.el-descriptions__title) {
      width: auto !important;
    }

    :deep(.el-descriptions__extra) {
      display: flex;
      flex-shrink: 0;
    }
  }
}

.basic-info-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}
</style>
