<template>
  <div class="tab-info">
    <div class="tab-info-title">
      <span>{{ $t('text_email_content') }}</span>
    </div>
    <el-scrollbar style="height: 100%">
      <div v-if="activeEmailInfo.messages && activeEmailInfo.messages.length > 0">
        <div v-for="(message, index) in activeEmailInfo.messages" :key="index" class="message-item">
          <div class="message-header">
            <div class="message-info">
              <div class="message-top">
                <span class="message-from">{{ message.message.from }}</span>
                <span class="message-time">{{
                  formatDateTime(
                    message.message.status === 'received'
                      ? message.message.received_at
                      : message.message.sent_at
                  )
                }}</span>
              </div>
              <div class="message-top">
                <div class="message-to">发送至 {{ message.message.to }}</div>
                <div class="message-status">
                  <el-tag
                    :type="message.message.status === 'sent' ? 'success' : 'info'"
                    size="small"
                  >
                    {{ message.message.status === 'sent' ? '已发送' : '已接收' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <div class="message-subject" v-if="message.message.subject">
            <strong>{{ message.message.subject }}</strong>
          </div>
          <div class="message-body" v-html="message.content.body"></div>

          <!-- 附件展示 -->
          <div
            v-if="message.content.attachments && message.content.attachments.length > 0"
            class="message-attachments"
          >
            <div class="attachment-title">附件：</div>
            <div
              v-for="attachment in message.content.attachments"
              :key="attachment.id"
              class="attachment-item"
            >
              <el-button
                :disabled="attachment.size > 50 * 1024 * 1024"
                size="small"
                type="primary"
                link
                @click="handleDownloadAttachment(attachment)"
              >
                📎 {{ attachment.file_name }} ({{ formatFileSize(attachment.size) }})
              </el-button>
            </div>
          </div>
          <el-divider v-if="index < activeEmailInfo.messages.length - 1" />
        </div>
      </div>
      <el-empty v-else description="暂无邮件内容" />
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface MessageAttachment {
  id: string;
  file_name: string;
  size: number;
  file_url?: string;
}

interface MessageContent {
  body: string;
  attachments?: MessageAttachment[];
}

interface Message {
  message: {
    from: string;
    to: string;
    subject?: string;
    status: 'sent' | 'received';
    sent_at?: string;
    received_at?: string;
  };
  content: MessageContent;
}

interface EmailInfo {
  messages?: Message[];
}

interface Props {
  activeEmailInfo: EmailInfo;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'download-attachment', attachment: MessageAttachment): void;
}>();

const { t: $t } = useI18n();

// 格式化完整日期时间- utc0时区
const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', { timeZone: 'UTC' });
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const handleDownloadAttachment = (attachment: MessageAttachment) => {
  emit('download-attachment', attachment);
};
</script>

<style lang="scss" scoped>
.tab-info {
  width: 100%;
  flex-grow: 1;
  overflow: auto;
  margin-bottom: 15px;

  .tab-info-title {
    font-size: 14px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
  }

  .message-item {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background-color: #fafafa;

    .message-header {
      margin-bottom: 10px;

      .message-info {
        display: flex;
        flex-direction: column;

        .message-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin-bottom: 5px;
        }

        .message-from {
          font-weight: bold;
          color: #303133;
        }

        .message-time {
          color: #909399;
          font-size: 12px;
        }

        .message-to {
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .message-subject {
      margin-bottom: 10px;
      font-size: 16px;
      color: #303133;
    }

    .message-body {
      margin-bottom: 15px;
      line-height: 1.6;
      color: #606266;

      :deep(p) {
        margin: 0 0 10px 0;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
      }
    }

    .message-attachments {
      .attachment-title {
        font-weight: bold;
        margin-bottom: 8px;
        color: #303133;
      }

      .attachment-item {
        margin-bottom: 5px;
      }
    }
  }
}
</style>
