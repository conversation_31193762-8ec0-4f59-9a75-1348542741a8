import { ref, reactive, computed } from 'vue';
import type { SearchForm, EmailItem, ChatListData, ReplyForm } from '../types/email';

export function useEmailData() {
  // 搜索表单数据
  const searchForm = ref<SearchForm>({
    email_id: undefined,
    page: 1,
    page_size: 20,
    keyword: '',
    sender_email: '',
    receiver_email: '',
    status: '',
    has_unread: undefined,
    has_attachments: undefined,
    priority: undefined,
    start_time: '',
    end_time: '',
    created_start_time: '',
    created_end_time: '',
    thread_id: '',
    tags: [],
    processor: [],
    remark_search: '',
    // 保留原有字段
    project: [],
    replied_at: [],
    last_login: [],
    tag: [],
    user_detail_remark: '',
    user_content: '',
    dsc_user_nickname: '',
    bot_ids: [],
    dsc_user_id: '',
    fpid: '',
    sid: '',
    bot_id: '',
    reply_type: undefined,
    last_reply_service: [],
    tag_type: null,
    tab_name: '',
    id: undefined,
    public: undefined,
  });

  // 邮件列表数据
  const chatListData = reactive<ChatListData>({
    list: [],
    total: 0,
    page: 1,
    pageSize: 20,
  });

  // 当前激活的邮件信息
  const activeEmailInfo = ref<EmailItem>({
    id: '',
    thread_id: '',
    subject: '',
    processor: '',
    status: '',
    tags: [],
    remark: '',
    messages: [],
  });

  // 回复表单数据
  const replyForm = ref<ReplyForm>({
    reply_type: '1',
    reply_body: '',
  });

  // 加载状态
  const poolLoading = ref(false);
  const replyLoading = ref(false);
  const isUploading = ref(false);

  // 选择相关状态
  const checkAll = ref(false);
  const checkEmailList = computed(() => {
    return chatListData.list.filter(item => item.checked).map(item => item.id);
  });

  // 排序相关
  const orderBy = ref('desc');
  const poolSortType = ref('created_at');

  // 重置搜索表单
  const resetSearchForm = () => {
    searchForm.value = {
      email_id: undefined,
      page: 1,
      page_size: 20,
      keyword: '',
      sender_email: '',
      receiver_email: '',
      status: '',
      has_unread: undefined,
      has_attachments: undefined,
      priority: undefined,
      start_time: '',
      end_time: '',
      created_start_time: '',
      created_end_time: '',
      thread_id: '',
      tags: [],
      processor: [],
      remark_search: '',
      // 保留原有字段
      project: [],
      replied_at: [],
      last_login: [],
      tag: [],
      user_detail_remark: '',
      user_content: '',
      dsc_user_nickname: '',
      bot_ids: [],
      dsc_user_id: '',
      fpid: '',
      sid: '',
      bot_id: '',
      reply_type: undefined,
      last_reply_service: [],
      tag_type: null,
      tab_name: '',
      id: undefined,
      public: undefined,
    };
  };

  // 重置回复表单
  const resetReplyForm = () => {
    replyForm.value = {
      reply_type: '1',
      reply_body: '',
    };
  };

  // 设置激活邮件
  const setActiveEmail = (email: EmailItem) => {
    // 清除之前的激活状态
    chatListData.list.forEach(item => {
      item.active = false;
    });

    // 设置新的激活状态
    const targetEmail = chatListData.list.find(item => item.id === email.id);
    if (targetEmail) {
      targetEmail.active = true;
    }

    activeEmailInfo.value = email;
  };

  // 更新邮件列表中的某个邮件
  const updateEmailInList = (updatedEmail: EmailItem) => {
    const index = chatListData.list.findIndex(item => item.id === updatedEmail.id);
    if (index !== -1) {
      chatListData.list[index] = { ...chatListData.list[index], ...updatedEmail };
    }
  };

  return {
    // 数据
    searchForm,
    chatListData,
    activeEmailInfo,
    replyForm,

    // 状态
    poolLoading,
    replyLoading,
    isUploading,
    checkAll,
    checkEmailList,
    orderBy,
    poolSortType,

    // 方法
    resetSearchForm,
    resetReplyForm,
    setActiveEmail,
    updateEmailInList,
  };
}
