{
  // 编辑器设置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },

  // 文件设置
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "files.trimFinalNewlines": true,

  // 语言特定设置
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // ESLint设置
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.format.enable": true,

  // Vue设置
  "vetur.validation.template": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false,

  // TypeScript设置
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  // "typescript.preferences.includePackageJsonAutoImports": "on",
  // "typescript.suggest.includeAutomaticOptionalChainCompletions": true,
  // "typescript.preferences.includeCompletionsForModuleExports": true,
  // "typescript.preferences.includeCompletionsWithSnippetText": true,
  // "typescript.suggest.includeCompletionsForImportStatements": true,

  // // Vue 高级设置
  // "vue.codeActions.enabled": true,
  // "vue.complete.casing.tags": "kebab",
  // "vue.complete.casing.props": "camel",
  // "vue.autoInsert.dotValue": true,
  // "vue.autoInsert.bracketSpacing": true,
  // "vue.server.hybridMode": true,

  // 搜索排除
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/auto-imports.d.ts": true,
    "**/components.d.ts": true
  },

  // 文件监视排除
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/.git/**": true
  }
}
